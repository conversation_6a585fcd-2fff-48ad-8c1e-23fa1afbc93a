'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useMessaging } from '@/hooks/use-messaging';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X,
  MessageCircle,
  Send,
  Search,
  Plus,
  ArrowLeft,
  Users,
  Loader2,
} from 'lucide-react';

import { toast } from 'sonner';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

interface ChatWidgetProps {
  onClose: () => void;
}

interface User {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  email: string | null;
}

export function ChatWidget({ onClose }: ChatWidgetProps) {
  const [currentView, setCurrentView] = useState<'conversations' | 'chat' | 'search'>('conversations');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Scroll için ref'ler
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    conversations,
    activeConversation,
    messages,
    isLoading,
    currentUser,
    sendMessage,
    createConversation,
    setActiveConversation,
    loadConversations,
  } = useMessaging();

  // Helper fonksiyonlar
  const getChatDisplayName = (chat: any) => {
    if (!chat.other_user || !currentUser) return 'Bilinmeyen Kullanıcı';
    return chat.other_user.full_name || chat.other_user.email || 'Bilinmeyen Kullanıcı';
  };

  // Kullanıcı ara (server action kullanarak)
  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);

      // Server action kullanarak ara
      const { searchUsers } = await import('@/lib/actions/messaging/chat-actions');
      const users = await searchUsers(query, 10);
      setSearchResults(users || []);
    } catch (error) {
      console.error('Kullanıcı arama hatası:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Arama sorgusu değiştiğinde ara
  useEffect(() => {
    const timer = setTimeout(() => {
      handleSearch(searchQuery);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchQuery, handleSearch]);

  // Konuşma seç
  const handleSelectConversation = useCallback((chat: any) => {
    setCurrentView('chat');
    setActiveConversation(chat);
  }, [setActiveConversation]);

  // Kullanıcı seç ve konuşma başlat
  const handleSelectUser = useCallback(async (userId: string) => {
    setIsSearching(true);
    try {
      const chatId = await createConversation(userId);
      if (chatId) {
        // Chat'leri yenile
        await loadConversations();
        // Yeni oluşturulan chat'i bul ve seç
        const newChat = conversations.find(c => c.id === chatId);
        if (newChat) {
          setActiveConversation(newChat);
        }
        setCurrentView('chat');
        setSearchQuery('');
      }
    } catch (error) {
      console.error('Konuşma başlatma hatası:', error);
      toast.error('Konuşma başlatılamadı');
    } finally {
      setIsSearching(false);
    }
  }, [createConversation, setActiveConversation, conversations]);

  // Scroll handler (basitleştirildi)
  const handleScroll = useCallback(() => {
    // Şimdilik basit scroll
  }, []);

  // En alta scroll et
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Mesajlar değiştiğinde en alta scroll et
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(scrollToBottom, 100);
    }
  }, [messages, scrollToBottom]);

  // Aktif konuşma değiştiğinde en alta scroll et
  useEffect(() => {
    if (activeConversation) {
      setTimeout(scrollToBottom, 200);
    }
  }, [activeConversation, scrollToBottom]);

  // Mesaj zamanını formatla
  const formatMessageTime = (dateString: string | null) => {
    if (!dateString) return '';
    try {
      return format(new Date(dateString), 'HH:mm', { locale: tr });
    } catch {
      return '';
    }
  };

  // Mesaj gönder
  const handleSendMessage = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !activeConversation || isSending) return;

    try {
      setIsSending(true);
      await sendMessage(newMessage.trim());
      setNewMessage('');
      // Mesaj gönderildikten sonra en alta scroll et
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error('Mesaj gönderme hatası:', error);
      toast.error('Mesaj gönderilemedi');
    } finally {
      setIsSending(false);
    }
  }, [newMessage, activeConversation, sendMessage, isSending, scrollToBottom]);



  // Kullanıcı adını al
  const getUserName = (user: User) => {
    return user.full_name || user.email || 'Bilinmeyen Kullanıcı';
  };

  // Kullanıcı başlangıç harflerini al
  const getUserInitials = (user: User) => {
    const name = getUserName(user);
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!currentUser) return null;
  return (
    <div className="bg-background flex h-[500px] w-full max-w-sm flex-col overflow-hidden rounded-xl border shadow-2xl transition-all duration-300">
      {/* Header */}
      <div className="from-primary to-primary/90 text-primary-foreground flex flex-shrink-0 items-center justify-between border-b bg-gradient-to-r p-3">
        <div className="flex min-w-0 items-center gap-2">
          <MessageCircle className="h-4 w-4 flex-shrink-0" />
          <span className="truncate text-sm font-medium">
            {currentView === 'chat' && activeConversation
              ? getChatDisplayName(activeConversation)
              : currentView === 'search'
                ? 'Kullanıcı Ara'
                : 'Mesajlar'}
          </span>
        </div>
        <div className="flex flex-shrink-0 items-center gap-1">
          {currentView === 'chat' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('conversations')}
              className="text-primary-foreground hover:bg-primary-foreground/20 h-6 w-6 p-0"
            >
              <ArrowLeft className="h-3 w-3" />
            </Button>
          )}
          {currentView === 'search' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView('conversations')}
              className="text-primary-foreground hover:bg-primary-foreground/20 h-6 w-6 p-0"
            >
              <ArrowLeft className="h-3 w-3" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-primary-foreground hover:bg-primary-foreground/20 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="bg-background flex min-h-0 flex-1 flex-col">
        {/* Conversations View */}
        {currentView === 'conversations' && (
          <>
            <div className="border-b p-3">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentView('search')}
                  className="flex-1"
                >
                  <Search className="mr-1 h-3 w-3" />
                  Ara
                </Button>
              </div>
            </div>
            <ScrollArea className="flex-1">
              <div className="p-2">
                {isLoading ? (
                  <div className="space-y-2">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="flex items-center gap-3 rounded-lg p-3"
                      >
                        <div className="bg-muted h-10 w-10 animate-pulse rounded-full" />
                        <div className="flex-1">
                          <div className="bg-muted mb-1 h-4 w-24 animate-pulse rounded" />
                          <div className="bg-muted h-3 w-32 animate-pulse rounded" />
                        </div>
                        <div className="bg-muted h-3 w-8 animate-pulse rounded" />
                      </div>
                    ))}
                  </div>
                ) : conversations.length === 0 ? (
                  <div className="py-8 text-center">
                    <MessageCircle className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                    <p className="text-muted-foreground text-sm">
                      Henüz konuşma yok
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentView('search')}
                      className="mt-2"
                    >
                      <Plus className="mr-1 h-3 w-3" />
                      Yeni Konuşma
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {conversations.map(chat => {
                      const displayName = getChatDisplayName(chat);

                      return (
                        <div
                          key={chat.id}
                          onClick={() => handleSelectConversation(chat)}
                          className="hover:bg-muted/50 hover:border-border flex cursor-pointer items-center gap-3 rounded-lg border border-transparent p-3 transition-colors"
                        >
                          <Avatar className="border-background h-10 w-10 border-2 shadow-sm">
                            <AvatarFallback className="from-primary/20 to-primary/10 bg-gradient-to-br text-xs font-medium">
                              {displayName.charAt(0).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <p className="truncate text-sm font-medium">
                              {displayName}
                            </p>
                            <p className="text-muted-foreground truncate text-xs">
                              {chat.last_message?.content || 'Birebir sohbet'}
                            </p>
                          </div>
                          <span className="text-muted-foreground text-xs">
                            {formatMessageTime(chat.last_message?.created_at || chat.created_at)}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </ScrollArea>
          </>
        )}

        {/* Chat View */}
        {currentView === 'chat' && activeConversation && (
          <div className="flex min-h-0 flex-1 flex-col">
            <div
              className="flex-1 p-2 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800"
              ref={scrollAreaRef}
              onScroll={handleScroll}
              style={{
                scrollbarWidth: 'thin',
                scrollbarColor: 'rgb(156 163 175) rgb(243 244 246)'
              }}
            >
              {isLoading ? (
                <div className="space-y-3 p-2">
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[75%] rounded-2xl px-3 py-2 ${
                          i % 2 === 0 ? 'bg-primary/20' : 'bg-muted'
                        }`}
                      >
                        <div className="mb-1 h-4 animate-pulse rounded bg-current opacity-20" />
                        <div className="h-3 w-16 animate-pulse rounded bg-current opacity-20" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-2">
                  {messages.length === 0 ? (
                    <div className="py-8 text-center">
                      <MessageCircle className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                      <p className="text-muted-foreground text-sm">
                        Henüz mesaj yok
                      </p>
                      <p className="text-muted-foreground text-xs">
                        İlk mesajı gönderin!
                      </p>
                    </div>
                  ) : (
                    <>
                      {messages.map(message => (
                        <div
                          key={message.id}
                          className={`flex ${
                            message.sender_id === currentUser?.id ? 'justify-end' : 'justify-start'
                          } mb-2`}
                        >
                          <div
                            className={`max-w-xs px-3 py-2 rounded-lg ${
                              message.sender_id === currentUser?.id
                                ? 'bg-primary text-primary-foreground'
                                : 'bg-muted text-muted-foreground'
                            }`}
                          >
                            {message.sender_id !== currentUser?.id && (
                              <div className="text-xs font-medium mb-1 opacity-75">
                                {message.sender?.full_name || message.sender?.email || 'Bilinmeyen'}
                              </div>
                            )}
                            <div className="text-sm">{message.content}</div>
                            <div className="text-xs opacity-75 mt-1">
                              {formatMessageTime(message.created_at)}
                            </div>
                          </div>
                        </div>
                      ))}
                      {/* Scroll marker */}
                      <div ref={messagesEndRef} className="h-1" />
                    </>
                  )}
                </div>
              )}
            </div>
            <div className="flex-shrink-0 border-t p-2 bg-background">
              <form onSubmit={handleSendMessage} className="flex gap-2">
                <Input
                  value={newMessage}
                  onChange={e => setNewMessage(e.target.value)}
                  placeholder="Mesaj yazın..."
                  className="h-8 min-w-0 flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  className="h-8 w-8 flex-shrink-0 p-0"
                  disabled={isSending || !newMessage.trim()}
                >
                  {isSending ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <Send className="h-3 w-3" />
                  )}
                </Button>
              </form>
            </div>
          </div>
        )}

        {/* Search View */}
        {currentView === 'search' && (
          <>
            <div className="border-b p-3">
              <Input
                placeholder="İsim veya e-posta ile ara..."
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className="h-8"
              />
            </div>
            <ScrollArea className="flex-1">
              <div className="p-2">
                {isSearching ? (
                  <div className="space-y-2">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center gap-2 p-2">
                        <div className="bg-muted h-8 w-8 animate-pulse rounded-full" />
                        <div className="flex-1">
                          <div className="bg-muted mb-1 h-3 w-20 animate-pulse rounded" />
                          <div className="bg-muted h-2 w-16 animate-pulse rounded" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : searchResults.length === 0 && searchQuery ? (
                  <div className="py-8 text-center">
                    <Users className="text-muted-foreground mx-auto mb-2 h-8 w-8" />
                    <p className="text-muted-foreground text-sm">
                      Kullanıcı bulunamadı
                    </p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {searchResults.map(user => (
                      <div
                        key={user.id}
                        onClick={() => handleSelectUser(user.id)}
                        className="hover:bg-muted/50 hover:border-border flex cursor-pointer items-center gap-3 rounded-lg border border-transparent p-3 transition-colors"
                      >
                        <Avatar className="border-background h-10 w-10 border-2 shadow-sm">
                          <AvatarImage src={user.avatar_url || undefined} />
                          <AvatarFallback className="from-primary/20 to-primary/10 bg-gradient-to-br text-xs font-medium">
                            {getUserInitials(user)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-sm font-medium">
                            {getUserName(user)}
                          </p>
                          {user.email && (
                            <p className="text-muted-foreground truncate text-xs">
                              {user.email}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </>
        )}
      </div>


    </div>
  );
}
