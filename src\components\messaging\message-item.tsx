'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';
import { MessageWithSender } from '@/types/messaging';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

import { cn } from '@/lib/utils';
import { MoreVertical, Clock, Check, CheckCheck, AlertCircle, Loader2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MessageItemProps {
  message: MessageWithSender;
  isOwn: boolean;
  showAvatar?: boolean;
  onEdit?: (message: MessageWithSender) => void;
  onDelete?: (message: MessageWithSender) => void;
  onReply?: (message: MessageWithSender) => void;
  onRetry?: (message: MessageWithSender) => void;
  className?: string;
}

export const MessageItem = React.memo<MessageItemProps>(
  ({ message, isOwn, showAvatar = true, onEdit, onDelete, onReply, onRetry, className }) => {
    const [showTime, setShowTime] = useState(false);

    const formatTime = (date: string | null) => {
      if (!date) return '';
      return format(new Date(date), 'HH:mm', { locale: tr });
    };

    const formatDate = (date: string | null) => {
      if (!date) return '';
      return format(new Date(date), 'dd MMMM yyyy, HH:mm', { locale: tr });
    };


    const handleEdit = () => {
      onEdit?.(message);
    };

    const handleDelete = () => {
      onDelete?.(message);
    };

    const handleReply = () => {
      onReply?.(message);
    };

    const handleRetry = () => {
      onRetry?.(message);
    };

    // WhatsApp benzeri mesaj durumu ikonu
    const getStatusIcon = () => {
      if (!isOwn) return null;

      switch (message.status) {
        case 'sending':
          return (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin text-white" />
            </div>
          );
        case 'sent':
          return (
            <div className="transition-all duration-200 ease-in-out">
              <Check className="h-3 w-3 text-muted-foreground" />
            </div>
          );
        case 'delivered':
          return (
            <div className="transition-all duration-200 ease-in-out">
              <CheckCheck className="h-3 w-3 text-white" />
            </div>
          );
        case 'read':
          return (
            <div className="transition-all duration-200 ease-in-out">
              <CheckCheck className="h-3 w-3 text-primary" />
            </div>
          );
        case 'failed':
          return (
            <div className="transition-all duration-200 ease-in-out">
              <AlertCircle className="h-3 w-3 text-destructive" />
            </div>
          );
        default:
          return (
            <div className="transition-all duration-200 ease-in-out">
              <Clock className="h-3 w-3 text-muted-foreground" />
            </div>
          );
      }
    };

    // WhatsApp benzeri mesaj durumu metni
    const getStatusText = () => {
      if (!isOwn) return '';

      switch (message.status) {
        case 'sending':
          return 'Gönderiliyor...';
        case 'sent':
          return ''; // Sadece ikon göster
        case 'delivered':
          return ''; // Sadece ikon göster
        case 'read':
          return ''; // Sadece ikon göster
        case 'failed':
          return 'Gönderilemedi';
        default:
          return '';
      }
    };

    // WhatsApp benzeri mesaj durumu rengi
    const getStatusColor = () => {
      switch (message.status) {
        case 'sending':
          return 'text-muted-foreground';
        case 'sent':
          return 'text-muted-foreground';
        case 'delivered':
          return 'text-muted-foreground';
        case 'read':
          return 'text-primary';
        case 'failed':
          return 'text-destructive';
        default:
          return 'text-muted-foreground';
      }
    };

    return (
      <div
        className={cn(
          'group hover:bg-muted/30 dark:hover:bg-muted/20 flex gap-3 px-4 py-2 transition-colors',
          isOwn && 'flex-row-reverse',
          className
        )}
        onClick={() => setShowTime(!showTime)}
      >
        {/* Mesaj içeriği */}
        <div
          className={cn('min-w-0 flex-1', isOwn && 'flex flex-col items-end')}
        >
          {/* Gönderen adı (kendi mesajı değilse) */}
          {!isOwn && showAvatar && (
            <div className="text-foreground dark:text-foreground/90 mb-1 text-sm font-medium">
              {message.sender?.full_name || 'Bilinmeyen Kullanıcı'}
            </div>
          )}

          {/* Yanıtlanan mesaj */}
          {message.reply_to && (
            <div className="text-muted-foreground dark:text-muted-foreground/80 line-clamp-2 text-sm">
              {message.reply_to.content}
            </div>
          )}

          {/* Mesaj balonu */}
          <div className="flex items-end">
            <Card
              className={cn(
                'group/message relative p-3 transition-all duration-300 ease-in-out',
                'shadow-sm hover:shadow-md dark:shadow-none dark:hover:shadow-none',
                isOwn
                  ? 'bg-primary text-primary-foreground ml-auto dark:bg-primary/90'
                  : 'bg-card text-card-foreground dark:bg-card/90',
                // WhatsApp benzeri durum efektleri
                message.isTemporary && message.status === 'sending' && 'opacity-90 scale-[0.98]',
                message.status === 'sent' && 'opacity-100 scale-100',
                message.status === 'delivered' && 'opacity-100 scale-100',
                message.status === 'failed' && 'border-destructive bg-destructive/10 opacity-80'
              )}
            >
              <div className="flex justify-between gap-2">
                <div className="text-sm break-words whitespace-pre-wrap flex-1">
                  {message.content}
                </div>
                <div className="flex flex-col justify-end items-end gap-1 flex-shrink-0">
                  <div className="flex items-center gap-1 text-xs">
                    <span className={cn(
                      isOwn ? "text-primary-foreground/70" : "text-muted-foreground"
                    )}>{formatTime(message.created_at)}</span>
                    {message.edited_at && (
                      <span className={cn(
                        "italic",
                        isOwn ? "text-primary-foreground/70" : "text-muted-foreground"
                      )}>(düzenlendi)</span>
                    )}
                  </div>

                  {/* WhatsApp benzeri durum gösterimi */}
                  {isOwn && (
                    <div className="flex items-center gap-1.5">
                      {/* Durum ikonu */}
                      <div className={cn("transition-all duration-200", getStatusColor())}>
                        {getStatusIcon()}
                      </div>

                      {/* Gönderiliyor metni */}
                      {message.status === 'sending' && (
                        <span className="text-xs text-white animate-pulse">
                          {getStatusText()}
                        </span>
                      )}

                      {/* Failed durumu için retry butonu */}
                      {message.status === 'failed' && (
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-destructive">
                            {getStatusText()}
                          </span>
                          {onRetry && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-4 px-1 text-xs text-destructive hover:text-destructive hover:bg-destructive/10 transition-colors"
                              onClick={handleRetry}
                            >
                              Yeniden Gönder
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Mesaj menüsü */}
              <div className="absolute -top-2 right-2 opacity-0 transition-opacity group-hover/message:opacity-100">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-6 w-6 rounded-full p-0"
                    >
                      <MoreVertical className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleReply}>
                      Yanıtla
                    </DropdownMenuItem>
                    {isOwn && (
                      <>
                        {message.status === 'failed' && onRetry && (
                          <DropdownMenuItem onClick={handleRetry}>
                            Yeniden Gönder
                          </DropdownMenuItem>
                        )}
                        {message.status !== 'failed' && (
                          <DropdownMenuItem onClick={handleEdit}>
                            Düzenle
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={handleDelete}
                        >
                          Sil
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </Card>
          </div>

          {/* Detaylı zaman bilgisi */}
          {showTime && (
            <div
              className={cn(
                'mt-1 text-xs',
                isOwn ? 'text-right text-muted-foreground/90' : 'text-muted-foreground',
                'dark:text-muted-foreground/70'
              )}
            >
              {formatDate(message.created_at)}
            </div>
          )}
        </div>

        {/* Kendi mesajı için avatar placeholder */}
        {showAvatar && isOwn && <div className="h-8 w-8 flex-shrink-0" />}
      </div>
    );
  }
);

MessageItem.displayName = 'MessageItem';
