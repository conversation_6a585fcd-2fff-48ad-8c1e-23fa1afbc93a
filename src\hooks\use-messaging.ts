'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { createClient } from '@/lib/supabase/client';
import { toast } from 'sonner';

// Te<PERSON>z mesaj<PERSON>ş<PERSON> tipleri
interface ChatMessage {
  id: string;
  chat_id: string;
  sender_id: string;
  content: string;
  message_type: string;
  created_at: string;
  sender?: {
    id: string;
    email: string;
    full_name?: string;
  };
}

interface Chat {
  id: string;
  name?: string;
  type: string;
  created_by: string;
  created_at: string;
  members?: Array<{
    user_id: string;
    user?: {
      id: string;
      email: string;
      full_name?: string;
    };
  }>;
  last_message?: Array<{
    id: string;
    content: string;
    created_at: string;
    sender?: {
      id: string;
      full_name?: string;
    };
  }>;
}

interface UseMessagingReturn {
  conversations: Chat[];
  activeConversation: Chat | null;
  messages: ChatMessage[];
  isLoading: boolean;
  error: string | null;
  currentUser: any;
  setActiveConversation: (chat: Chat | null) => void;
  sendMessage: (content: string) => Promise<void>;
  createConversation: (participantIds: string[], name?: string) => Promise<string | undefined>;
  loadConversations: () => Promise<void>;
}

export function useMessaging(): UseMessagingReturn {
  const [conversations, setConversations] = useState<Chat[]>([]);
  const [activeConversation, setActiveConversation] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  const supabase = createClient();
  const subscriptionRef = useRef<any>(null);

  // Kullanıcı bilgisini al
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };
    getUser();
  }, [supabase]);

  // Chat'leri yükle
  const loadConversations = useCallback(async () => {
    if (!currentUser) return;

    try {
      setIsLoading(true);
      
      // Önce kullanıcının chat'lerini al
      const { data: userChats, error: userChatsError } = await supabase
        .from('chat_members')
        .select('chat_id')
        .eq('user_id', currentUser.id)
        .eq('is_active', true);

      if (userChatsError) {
        console.error('Kullanıcı chat\'leri alınırken hata:', userChatsError);
        setError('Chat\'ler yüklenemedi');
        return;
      }

      const chatIds = userChats?.map(uc => uc.chat_id) || [];

      if (chatIds.length === 0) {
        setConversations([]);
        setError(null);
        return;
      }

      // Chat'leri ve üyelerini al
      const { data, error } = await supabase
        .from('chats')
        .select(`
          *,
          members:chat_members(
            user_id,
            user:profiles(id, email, full_name)
          )
        `)
        .in('id', chatIds)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Chat\'ler yüklenirken hata:', error);
        setError('Chat\'ler yüklenemedi');
        return;
      }

      setConversations(data || []);
      setError(null);
    } catch (err) {
      console.error('Chat\'ler yüklenirken hata:', err);
      setError('Chat\'ler yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser, supabase]);

  // Mesajları yükle
  const loadMessages = useCallback(async (chatId: string) => {
    if (!chatId) return;

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          sender:profiles(id, email, full_name)
        `)
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Mesajlar yüklenirken hata:', error);
        setError('Mesajlar yüklenemedi');
        return;
      }

      setMessages(data || []);
      setError(null);
    } catch (err) {
      console.error('Mesajlar yüklenirken hata:', err);
      setError('Mesajlar yüklenemedi');
    }
  }, [supabase]);

  // Aktif chat'i seç
  const selectChat = useCallback((chat: Chat | null) => {
    setActiveConversation(chat);
    if (chat) {
      loadMessages(chat.id);
    } else {
      setMessages([]);
    }
  }, [loadMessages]);

  // Mesaj gönder
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || !activeConversation || !currentUser) return;

    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          chat_id: activeConversation.id,
          sender_id: currentUser.id,
          content: content.trim(),
          message_type: 'text',
        })
        .select(`
          *,
          sender:profiles(id, email, full_name)
        `)
        .single();

      if (error) {
        console.error('Mesaj gönderme hatası:', error);
        toast.error('Mesaj gönderilemedi');
        return;
      }

      console.log('Mesaj başarıyla gönderildi:', data);
      
      // Optimistic update - mesajı hemen ekle
      setMessages(prev => [...prev, data]);
      
      // Chat'in updated_at'ini güncelle
      await supabase
        .from('chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', activeConversation.id);

      toast.success('Mesaj gönderildi');
    } catch (err) {
      console.error('Mesaj gönderme hatası:', err);
      toast.error('Mesaj gönderilemedi');
    }
  }, [activeConversation, currentUser, supabase]);

  // Chat oluştur
  const createConversation = useCallback(async (participantIds: string[], name?: string) => {
    if (!currentUser) return;

    try {
      // Chat oluştur
      const { data: chat, error: chatError } = await supabase
        .from('chats')
        .insert({
          name: name || null,
          type: participantIds.length === 1 ? 'direct' : 'group',
          created_by: currentUser.id,
        })
        .select()
        .single();

      if (chatError) {
        console.error('Chat oluşturma hatası:', chatError);
        toast.error('Chat oluşturulamadı');
        return;
      }

      // Üyeleri ekle (kendini de dahil et)
      const allMembers = [currentUser.id, ...participantIds];
      const membersData = allMembers.map(userId => ({
        chat_id: chat.id,
        user_id: userId,
      }));

      const { error: membersError } = await supabase
        .from('chat_members')
        .insert(membersData);

      if (membersError) {
        console.error('Üye ekleme hatası:', membersError);
        toast.error('Üyeler eklenemedi');
        return;
      }

      // Chat'leri yenile
      await loadConversations();
      toast.success('Chat oluşturuldu');
      
      return chat.id;
    } catch (err) {
      console.error('Chat oluşturma hatası:', err);
      toast.error('Chat oluşturulamadı');
    }
  }, [currentUser, supabase, loadConversations]);

  // Realtime subscription
  useEffect(() => {
    if (!currentUser || !activeConversation) return;

    console.log('Realtime subscription kuruluyor:', activeConversation.id);

    const channel = supabase
      .channel(`chat-messages-${activeConversation.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `chat_id=eq.${activeConversation.id}`,
        },
        async (payload) => {
          console.log('Yeni mesaj alındı:', payload);
          
          // Sender bilgisini al
          const { data: sender } = await supabase
            .from('profiles')
            .select('id, email, full_name')
            .eq('id', payload.new.sender_id)
            .single();

          const newMessage = {
            ...payload.new,
            sender: sender || undefined,
          } as ChatMessage;

          setMessages(prev => {
            // Duplicate kontrolü
            const exists = prev.some(msg => msg.id === newMessage.id);
            if (exists) return prev;
            
            return [...prev, newMessage];
          });
        }
      )
      .subscribe((status) => {
        console.log('Subscription durumu:', status);
      });

    subscriptionRef.current = channel;

    return () => {
      console.log('Realtime subscription kapatılıyor');
      channel.unsubscribe();
    };
  }, [currentUser, activeConversation, supabase]);

  // İlk yükleme
  useEffect(() => {
    if (currentUser) {
      loadConversations();
    }
  }, [currentUser, loadConversations]);

  return {
    conversations,
    activeConversation,
    messages,
    isLoading,
    error,
    currentUser,
    setActiveConversation: selectChat,
    sendMessage,
    createConversation,
    loadConversations,
  };
}
