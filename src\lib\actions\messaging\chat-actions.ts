'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

export interface SendMessageResponse {
  success: boolean;
  message?: any;
  error?: string;
}

export interface CreateChatResponse {
  success: boolean;
  chat?: any;
  error?: string;
}

export interface EditMessageResponse {
  success: boolean;
  error?: string;
}

export interface DeleteMessageResponse {
  success: boolean;
  error?: string;
}

/**
 * Mesaj gönder
 */
export async function sendMessage(
  chatId: string,
  content: string
): Promise<SendMessageResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Mesajı ekle (RLS politikası erişimi kontrol edecek)
    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert({
        chat_id: chatId,
        sender_id: user.id,
        content: content.trim(),
      })
      .select(`
        *,
        sender:profiles(id, email, full_name)
      `)
      .single();

    if (error) {
      console.error('Mesaj ekleme hatası:', error);
      return { success: false, error: 'Mesaj gönderilemedi' };
    }

    revalidatePath('/dashboard/messages');
    return { success: true, message };
  } catch (error) {
    console.error('Mesaj gönderme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Chat oluştur - sadece birebir sohbetler
 */
export async function createChat(
  otherUserId: string
): Promise<CreateChatResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    if (!otherUserId) {
      return { success: false, error: 'Diğer kullanıcı gerekli' };
    }

    if (user.id === otherUserId) {
      return { success: false, error: 'Kendinizle sohbet oluşturamazsınız' };
    }

    // Diğer kullanıcının geçerli olduğunu kontrol et
    const { data: otherUser, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', otherUserId)
      .single();

    if (userError || !otherUser) {
      return { success: false, error: 'Geçersiz kullanıcı' };
    }

    // Chat oluştur veya mevcut olanı getir
    const { data: chatId, error } = await supabase.rpc('create_or_get_chat', {
      other_user_id: otherUserId
    });

    if (error) {
      console.error('Chat oluşturma hatası:', error);
      return { success: false, error: 'Chat oluşturulamadı' };
    }

    // Chat detaylarını getir
    const { data: chat, error: chatError } = await supabase
      .from('chats')
      .select('*')
      .eq('id', chatId)
      .single();

    if (chatError) {
      console.error('Chat detayları alınamadı:', chatError);
      return { success: false, error: 'Chat detayları alınamadı' };
    }

    revalidatePath('/dashboard');
    return { success: true, chat };
  } catch (error) {
    console.error('Chat oluşturma hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Kullanıcının chat'lerini getir
 */
export async function getUserChats() {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // Yeni basit chat sistemini kullan
    const { data, error } = await supabase.rpc('get_user_chats');

    if (error) {
      console.error('Chat\'ler getirme hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Chat\'ler getirme hatası:', error);
    return [];
  }
}

/**
 * Chat mesajlarını getir
 */
export async function getChatMessages(chatId: string) {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // RLS politikası erişimi kontrol edecek, ayrı kontrol gerekmez
    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        sender:profiles(id, email, full_name)
      `)
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Mesajlar getirme hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Mesajlar getirme hatası:', error);
    return [];
  }
}

/**
 * Mesaj düzenle
 */
export async function editMessage(
  messageId: string,
  newContent: string
): Promise<EditMessageResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    if (!newContent.trim()) {
      return { success: false, error: 'Mesaj içeriği boş olamaz' };
    }

    // Mesajı düzenle
    const { error } = await supabase.rpc('edit_message', {
      message_id: messageId,
      new_content: newContent.trim()
    });

    if (error) {
      console.error('Mesaj düzenleme hatası:', error);
      return { success: false, error: 'Mesaj düzenlenemedi' };
    }

    revalidatePath('/dashboard/messages');
    return { success: true };
  } catch (error) {
    console.error('Mesaj düzenleme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Mesaj sil
 */
export async function deleteMessage(
  messageId: string
): Promise<DeleteMessageResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Mesajı sil
    const { error } = await supabase.rpc('delete_message', {
      message_id: messageId
    });

    if (error) {
      console.error('Mesaj silme hatası:', error);
      return { success: false, error: 'Mesaj silinemedi' };
    }

    revalidatePath('/dashboard/messages');
    return { success: true };
  } catch (error) {
    console.error('Mesaj silme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Kullanıcı ara
 */
export async function searchUsers(query: string, limit: number = 10) {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    if (!query.trim()) {
      return [];
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, avatar_url')
      .or(`email.ilike.%${query}%,full_name.ilike.%${query}%`)
      .neq('id', user.id) // Kendini hariç tut
      .limit(limit);

    if (error) {
      console.error('Kullanıcı arama hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Kullanıcı arama hatası:', error);
    return [];
  }
}
