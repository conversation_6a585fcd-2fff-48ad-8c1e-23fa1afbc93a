'use server';

import { createClient } from '@/lib/supabase/server';
import { revalidatePath } from 'next/cache';

export interface SendMessageResponse {
  success: boolean;
  message?: any;
  error?: string;
}

export interface CreateChatResponse {
  success: boolean;
  chat?: any;
  error?: string;
}

/**
 * <PERSON><PERSON> gönder
 */
export async function sendMessage(
  chatId: string,
  content: string
): Promise<SendMessageResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Kullanıcının bu chat'e erişimi var mı kontrol et
    const { data: membership, error: membershipError } = await supabase
      .from('chat_members')
      .select('id')
      .eq('chat_id', chatId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (membershipError || !membership) {
      return { success: false, error: 'Bu chat\'e erişim yetkiniz yok' };
    }

    // Mesajı ekle
    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert({
        chat_id: chatId,
        sender_id: user.id,
        content: content.trim(),
        message_type: 'text',
      })
      .select(`
        *,
        sender:profiles(id, email, full_name)
      `)
      .single();

    if (error) {
      console.error('Mesaj ekleme hatası:', error);
      return { success: false, error: 'Mesaj gönderilemedi' };
    }

    // Chat'in updated_at'ini güncelle
    await supabase
      .from('chats')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', chatId);

    revalidatePath('/dashboard/messages');
    return { success: true, message };
  } catch (error) {
    console.error('Mesaj gönderme hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Chat oluştur
 */
export async function createChat(
  participantIds: string[],
  name?: string
): Promise<CreateChatResponse> {
  try {
    const supabase = await createClient();

    // Kullanıcı kimlik doğrulama
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: 'Kimlik doğrulama hatası' };
    }

    // Katılımcı sayısını kontrol et
    if (participantIds.length === 0) {
      return { success: false, error: 'En az bir katılımcı gerekli' };
    }

    // Katılımcıların geçerli olduğunu kontrol et
    const { data: validUsers, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .in('id', participantIds);

    if (userError || !validUsers || validUsers.length !== participantIds.length) {
      return { success: false, error: 'Geçersiz katılımcılar' };
    }

    // Birebir konuşma için mevcut chat kontrolü
    if (participantIds.length === 1) {
      const { data: existingChat } = await supabase
        .from('chat_members')
        .select(`
          chat_id,
          chat:chats!inner(id, type)
        `)
        .eq('user_id', user.id)
        .eq('is_active', true);

      if (existingChat) {
        for (const membership of existingChat) {
          if ((membership.chat as any)?.type === 'direct') {
            // Bu chat'te diğer katılımcı var mı kontrol et
            const { data: otherMember } = await supabase
              .from('chat_members')
              .select('user_id')
              .eq('chat_id', membership.chat_id)
              .eq('user_id', participantIds[0])
              .eq('is_active', true)
              .single();

            if (otherMember) {
              // Mevcut chat'i döndür
              return { success: true, chat: membership.chat };
            }
          }
        }
      }
    }

    // Transaction ile chat ve üyeleri birlikte oluştur
    const { data, error } = await supabase.rpc('create_chat_with_members', {
      participant_ids: [user.id, ...participantIds],
      chat_name: name || null,
      chat_type: participantIds.length === 1 ? 'direct' : 'group'
    });

    if (error) {
      console.error('Chat oluşturma hatası:', error);

      // Fallback: Manuel chat oluşturma
      const { data: chat, error: chatError } = await supabase
        .from('chats')
        .insert({
          name: name || null,
          type: participantIds.length === 1 ? 'direct' : 'group',
          created_by: user.id,
        })
        .select()
        .single();

      if (chatError) {
        console.error('Manuel chat oluşturma hatası:', chatError);
        return { success: false, error: 'Chat oluşturulamadı' };
      }

      // Üyeleri ekle (kendini de dahil et)
      const allMembers = [user.id, ...participantIds];
      const membersData = allMembers.map(userId => ({
        chat_id: chat.id,
        user_id: userId,
      }));

      const { error: membersError } = await supabase
        .from('chat_members')
        .insert(membersData);

      if (membersError) {
        console.error('Üye ekleme hatası:', membersError);
        // Chat'i sil
        await supabase.from('chats').delete().eq('id', chat.id);
        return { success: false, error: 'Üyeler eklenemedi' };
      }

      return { success: true, chat };
    }

    revalidatePath('/dashboard');
    return { success: true, chat: data };
  } catch (error) {
    console.error('Chat oluşturma hatası:', error);
    return { success: false, error: 'Beklenmeyen bir hata oluştu' };
  }
}

/**
 * Kullanıcının chat'lerini getir
 */
export async function getUserChats() {
  try {
    const supabase = await createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    const { data, error } = await supabase
      .from('chats')
      .select(`
        *,
        members:chat_members(
          user_id,
          user:profiles(id, email, full_name)
        )
      `)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Chat\'ler getirme hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Chat\'ler getirme hatası:', error);
    return [];
  }
}

/**
 * Chat mesajlarını getir
 */
export async function getChatMessages(chatId: string) {
  try {
    const supabase = await createClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    // Kullanıcının bu chat'e erişimi var mı kontrol et
    const { data: membership } = await supabase
      .from('chat_members')
      .select('id')
      .eq('chat_id', chatId)
      .eq('user_id', user.id)
      .eq('is_active', true)
      .single();

    if (!membership) {
      return [];
    }

    const { data, error } = await supabase
      .from('chat_messages')
      .select(`
        *,
        sender:profiles(id, email, full_name)
      `)
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Mesajlar getirme hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Mesajlar getirme hatası:', error);
    return [];
  }
}

/**
 * Kullanıcı ara
 */
export async function searchUsers(query: string, limit: number = 10) {
  try {
    const supabase = await createClient();

    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return [];
    }

    if (!query.trim()) {
      return [];
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, avatar_url')
      .or(`email.ilike.%${query}%,full_name.ilike.%${query}%`)
      .neq('id', user.id) // Kendini hariç tut
      .limit(limit);

    if (error) {
      console.error('Kullanıcı arama hatası:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Kullanıcı arama hatası:', error);
    return [];
  }
}
