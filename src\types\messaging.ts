/**
 * Basitleştirilmiş mesajlaşma sistemi tip tanımları - sadece birebir sohbetler
 */

import { Database } from '@/lib/supabase/types';

// Veritabanı tablolarından tip çıkarımı
type Tables = Database['public']['Tables'];

// Yeni basit chat sistemi tipleri
export type Chat = {
  id: string;
  user1_id: string;
  user2_id: string;
  created_at: string;
  updated_at: string;
};

export type ChatMessage = {
  id: string;
  chat_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  updated_at: string | null;
  is_edited?: boolean;
  is_deleted?: boolean;
  deleted_at?: string | null;
};


// Genişletilmiş tipler
export interface ChatWithOtherUser extends Chat {
  other_user: {
    id: string;
    email: string;
    full_name: string | null;
    avatar_url: string | null;
  };
  last_message?: {
    content: string;
    created_at: string;
  };
}

export interface ChatMessageWithSender extends ChatMessage {
  sender: {
    id: string;
    email: string;
    full_name: string | null;
    avatar_url?: string | null;
  } | null;
  // UI durumları (optimistic update için)
  status?: MessageStatus;
  isTemporary?: boolean;
}

// Mesaj durumları
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

// Realtime event tipleri
export interface RealtimeMessageEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: ChatMessage;
  old?: ChatMessage;
}

// Hook return tipleri
export interface UseMessagingReturn {
  conversations: ChatWithOtherUser[];
  activeConversation: ChatWithOtherUser | null;
  messages: ChatMessageWithSender[];
  isLoading: boolean;
  error: string | null;
  currentUser: any;
  setActiveConversation: (chat: ChatWithOtherUser | null) => void;
  sendMessage: (content: string) => Promise<void>;
  createConversation: (otherUserId: string) => Promise<string | undefined>;
  loadConversations: () => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
}



// API Response tipleri
export interface SendMessageResponse {
  success: boolean;
  message?: ChatMessageWithSender;
  error?: string;
}

export interface CreateChatResponse {
  success: boolean;
  chat?: Chat;
  error?: string;
}

export interface EditMessageResponse {
  success: boolean;
  error?: string;
}

export interface DeleteMessageResponse {
  success: boolean;
  error?: string;
}

// Form tipleri
export interface SendMessageForm {
  content: string;
}

export interface CreateChatForm {
  otherUserId: string;
}

// Notification tipleri
export interface MessageNotification {
  id: string;
  title: string;
  body: string;
  chatId: string;
  senderId: string;
  timestamp: string;
}
