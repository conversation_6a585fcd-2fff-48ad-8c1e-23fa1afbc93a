/**
 * Mesajlaşma sistemi tip tanımları
 */

import { Database } from '@/lib/supabase/types';

// Veritabanı tablolarından tip çıkarımı
type Tables = Database['public']['Tables'];

export type Conversation = Tables['conversations']['Row'];
export type ConversationInsert = Tables['conversations']['Insert'];
export type ConversationUpdate = Tables['conversations']['Update'];

export type ConversationParticipant = Tables['conversation_participants']['Row'];
export type ConversationParticipantInsert = Tables['conversation_participants']['Insert'];
export type ConversationParticipantUpdate = Tables['conversation_participants']['Update'];

export type Message = Tables['messages']['Row'];
export type MessageInsert = Tables['messages']['Insert'];
export type MessageUpdate = Tables['messages']['Update'];


// Genişletilmiş tipler
export interface ConversationWithParticipants extends Conversation {
  participants: (ConversationParticipant & {
    profile: {
      id: string;
      full_name: string | null;
      avatar_url: string | null;
      email: string | null;
    };
  })[];
  last_message?: MessageWithSender;
  unread_count?: number;
}

export interface MessageWithSender extends Message {
  sender: {
    id: string;
    full_name: string | null;
    avatar_url: string | null;
  } | null;
  reply_to?: MessageWithSender;
  // UI durumları (optimistic update için)
  status?: MessageStatus;
  isTemporary?: boolean;
}

export interface ConversationWithMessages extends ConversationWithParticipants {
  messages: MessageWithSender[];
}

// Mesaj tipleri
export type MessageType = 'text' | 'image' | 'file' | 'system';

// Mesaj durumları
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

// Konuşma tipleri
export type ConversationType = 'direct' | 'group';

// Realtime event tipleri
export interface RealtimeMessageEvent {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: Message;
  old?: Message;
}



// Hook return tipleri
export interface UseMessagingReturn {
  conversations: ConversationWithParticipants[];
  activeConversation: ConversationWithMessages | null;
  messages: MessageWithSender[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (content: string, conversationId: string) => Promise<void>;
  createConversation: (participantIds: string[], name?: string) => Promise<string>;
  setActiveConversation: (conversationId: string | null) => void;
  // Pagination özellikleri
  hasMoreMessages: boolean;
  isLoadingMoreMessages: boolean;
  loadMoreMessages: () => Promise<void>;
  // Mesaj düzenleme/silme
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
  retryMessage: (failedMessage: MessageWithSender) => Promise<void>;
}



// API Response tipleri
export interface SendMessageResponse {
  success: boolean;
  message?: MessageWithSender;
  error?: string;
}

export interface CreateConversationResponse {
  success: boolean;
  conversation?: { id: string };
  error?: string;
}

// Form tipleri
export interface SendMessageForm {
  content: string;
  replyToId?: string;
}

export interface CreateConversationForm {
  participantIds: string[];
  name?: string;
  type: ConversationType;
}

// Notification tipleri
export interface MessageNotification {
  id: string;
  title: string;
  body: string;
  conversationId: string;
  senderId: string;
  timestamp: string;
}
